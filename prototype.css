/* prototype.css */
@import url("https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "SF Pro SC", "SF Pro Text", "SF Pro Icons", "PingFang SC",
    "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

body {
  font-family: "SF Pro SC", "SF Pro Text", "SF Pro Icons", "PingFang SC",
    "Helvetica Neue", "Helvetica", "Arial", sans-serif;
  background: #0a0a0a;
  color: #fff;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* 动态背景效果 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    #0a1428 0%,
    #1a2a4a 25%,
    #2a3a5a 50%,
    #1a2a4a 75%,
    #0a1428 100%
  );
  z-index: -2;
}

.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #00ffff;
  border-radius: 50%;
  animation: float 6s infinite linear;
  opacity: 0.6;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* 主容器 */
.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
  z-index: 1;
}

/* 顶部HUD风格头部 */
.header {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 255, 0.1) 0%,
    rgba(0, 150, 255, 0.1) 100%
  );
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 20px;
  padding: 25px 40px;
  margin-bottom: 30px;
  backdrop-filter: blur(20px);
  box-shadow: 0 0 40px rgba(0, 255, 255, 0.2),
    inset 0 0 40px rgba(0, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.header::before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #00ffff, #0080ff, #8000ff, #ff0080);
  border-radius: 20px;
  z-index: -1;
  animation: borderGlow 3s infinite alternate;
}

@keyframes borderGlow {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.8;
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo-hologram {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #00ffff, #0080ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  position: relative;
  animation: pulse 2s infinite;
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.6);
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.logo-text h1 {
  background: linear-gradient(45deg, #00ffff, #ffffff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 32px;
  font-weight: 900;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.logo-text p {
  color: #00ffff;
  font-size: 14px;
  margin-top: 5px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.user-hud {
  display: flex;
  align-items: center;
  gap: 20px;
  background: rgba(0, 255, 255, 0.1);
  padding: 15px 25px;
  border-radius: 15px;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.user-avatar-holo {
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, #ff00ff, #00ffff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  position: relative;
  animation: rotate 10s infinite linear;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 语音交互中心 */
.voice-center {
  text-align: center;
  margin: 50px 0;
  position: relative;
}

.voice-orb {
  width: 200px;
  height: 200px;
  margin: 0 auto 30px;
  background: radial-gradient(
    circle,
    rgba(0, 255, 255, 0.3) 0%,
    rgba(0, 150, 255, 0.1) 100%
  );
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid rgba(0, 255, 255, 0.5);
  box-shadow: 0 0 50px rgba(0, 255, 255, 0.4),
    inset 0 0 50px rgba(0, 255, 255, 0.2);
}

.voice-orb:hover {
  transform: scale(1.1);
  box-shadow: 0 0 80px rgba(0, 255, 255, 0.6),
    inset 0 0 50px rgba(0, 255, 255, 0.3);
}

.voice-orb.active {
  animation: voicePulse 1s infinite;
}

@keyframes voicePulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 50px rgba(0, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 100px rgba(0, 255, 255, 0.8);
  }
}

.voice-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 60px;
  color: #00ffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
}

.voice-wave {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  animation: wave 2s infinite;
}

@keyframes wave {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

.voice-instruction {
  font-size: 18px;
  color: #00ffff;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.voice-status {
  font-size: 14px;
  color: #888;
  min-height: 20px;
}

/* 全息功能模块 */
.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin: 50px 0;
}

.module-card {
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(20, 20, 50, 0.8) 100%
  );
  border-radius: 20px;
  padding: 30px;
  border: 1px solid transparent;
  position: relative;
  cursor: pointer;
  transition: all 0.4s ease;
  backdrop-filter: blur(20px);
  overflow: hidden;
}

.module-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.module-card:hover::before {
  transform: translateX(100%);
}

.module-card:hover {
  transform: translateY(-10px);
}

/* 规章检索模块 - 蓝色科技风 */
.module-regulations {
  border-color: #0080ff;
  box-shadow: 0 10px 40px rgba(0, 128, 255, 0.3);
}

.module-regulations:hover {
  box-shadow: 0 20px 60px rgba(0, 128, 255, 0.5);
}

.module-regulations .module-icon {
  background: linear-gradient(45deg, #0080ff, #00ffff);
}

/* 教学资源模块 - 彩虹渐变 */
.module-resources {
  border-color: #ff6b35;
  box-shadow: 0 10px 40px rgba(255, 107, 53, 0.3);
}

.module-resources:hover {
  box-shadow: 0 20px 60px rgba(255, 107, 53, 0.5);
}

.module-resources .module-icon {
  background: linear-gradient(45deg, #ff6b35, #f7931e, #ffcc02);
}

/* 随堂出题模块 - 紫色魔法风 */
.module-questions {
  border-color: #8000ff;
  box-shadow: 0 10px 40px rgba(128, 0, 255, 0.3);
}

.module-questions:hover {
  box-shadow: 0 20px 60px rgba(128, 0, 255, 0.5);
}

.module-questions .module-icon {
  background: linear-gradient(45deg, #8000ff, #ff00ff);
}

/* 备课平台模块 - 绿色生态风 */
.module-planning {
  border-color: #00ff80;
  box-shadow: 0 10px 40px rgba(0, 255, 128, 0.3);
}

.module-planning:hover {
  box-shadow: 0 20px 60px rgba(0, 255, 128, 0.5);
}

.module-planning .module-icon {
  background: linear-gradient(45deg, #00ff80, #80ff00);
}

.module-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: white;
  margin-bottom: 20px;
  position: relative;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
  animation: iconFloat 3s infinite ease-in-out;
}

@keyframes iconFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.module-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.module-description {
  font-size: 14px;
  line-height: 1.6;
  opacity: 0.8;
  margin-bottom: 20px;
}

.module-features {
  list-style: none;
}

.module-features li {
  padding: 5px 0;
  font-size: 12px;
  color: #ccc;
  position: relative;
  padding-left: 20px;
}

.module-features li::before {
  content: "▶";
  position: absolute;
  left: 0;
  color: inherit;
}

/* 语音识别可视化 */
.voice-visualizer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3px;
  margin: 20px 0;
  height: 40px;
}

.bar {
  width: 4px;
  background: linear-gradient(to top, #00ffff, #0080ff);
  border-radius: 2px;
  animation: audioBar 1s infinite ease-in-out;
}

.bar:nth-child(1) {
  animation-delay: 0.1s;
}
.bar:nth-child(2) {
  animation-delay: 0.2s;
}
.bar:nth-child(3) {
  animation-delay: 0.3s;
}
.bar:nth-child(4) {
  animation-delay: 0.4s;
}
.bar:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes audioBar {
  0%,
  100% {
    height: 5px;
  }
  50% {
    height: 35px;
  }
}

/* AI助手状态指示器 */
.ai-status {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(0, 255, 255, 0.5);
  border-radius: 15px;
  padding: 15px 20px;
  backdrop-filter: blur(20px);
  z-index: 1000;
}

.ai-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #00ff00;
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

/* 全息投影效果 */
.hologram-effect {
  position: relative;
}

.hologram-effect::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 255, 0.05) 2px,
    rgba(0, 255, 255, 0.05) 4px
  );
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modules-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .voice-orb {
    width: 150px;
    height: 150px;
  }

  .voice-icon {
    font-size: 40px;
  }

  .module-card {
    padding: 20px;
  }
}

/* 加载动画 */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #0a0a0a;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 1;
  transition: opacity 1s ease;
}

.loading-screen.hidden {
  opacity: 0;
  pointer-events: none;
}

.loader {
  width: 100px;
  height: 100px;
  border: 3px solid rgba(0, 255, 255, 0.1);
  border-top: 3px solid #00ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-card {
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.95) 0%,
    rgba(20, 20, 50, 0.95) 50%,
    rgba(40, 20, 60, 0.95) 100%
  );
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 24px;
  padding: 24px 64px;
  width: 100%;
  max-width: 640px;
  backdrop-filter: blur(25px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5), 0 0 40px rgba(0, 255, 255, 0.2),
    inset 0 0 40px rgba(0, 255, 255, 0.05);
  position: relative;
  text-align: center;
}

.login-card::before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #00ffff, #0080ff, #8000ff, #ff0080);
  border-radius: 20px;
  z-index: -1;
  animation: borderGlow 3s infinite alternate;
}

.login-title {
  background: linear-gradient(135deg, #00ffff, #ffffff, #8000ff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 32px;
  font-weight: 900;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  margin-bottom: 60px;
  text-transform: uppercase;
  letter-spacing: 3px;
  position: relative;
}

.login-title::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
  border-radius: 2px;
}

.form-group {
  margin-bottom: 30px;
  text-align: left;
}

.form-label {
  display: block;
  color: #00ffff;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.form-input {
  width: 100%;
  padding: 18px 24px;
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(0, 255, 255, 0.2);
  border-radius: 12px;
  color: #fff;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.form-input:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 0 4px rgba(0, 255, 255, 0.1),
    0 8px 32px rgba(0, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.8);
  transform: translateY(-2px);
}

.form-input:hover {
  border-color: rgba(0, 255, 255, 0.4);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.4);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

.upload-area {
  border: 2px dashed rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  padding: 40px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(0, 0, 0, 0.4);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.upload-area::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.upload-area:hover {
  border-color: #00ffff;
  background: rgba(0, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 255, 255, 0.2);
}

.upload-area:hover::before {
  left: 100%;
}

.upload-text {
  color: #00ffff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.upload-text::before {
  content: "📁";
  font-size: 24px;
}

.upload-hint {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  font-style: italic;
}

.login-button {
  width: 100%;
  padding: 20px;
  background: linear-gradient(135deg, #00ffff, #0080ff, #8000ff);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 40px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
}

.login-button:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 255, 255, 0.5);
  background: linear-gradient(135deg, #00ffff, #0080ff, #ff00ff);
}

.login-button:active {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(0, 255, 255, 0.4);
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.copyright {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  text-align: center;
}

/* 文件上传隐藏input */
.file-input {
  display: none;
}

/* 表单验证状态 */
.form-input.error {
  border-color: #ff4444;
  box-shadow: 0 0 0 4px rgba(255, 68, 68, 0.1);
}

.form-input.success {
  border-color: #00ff80;
  box-shadow: 0 0 0 4px rgba(0, 255, 128, 0.1);
}

/* 加载状态 */
.login-button.loading {
  pointer-events: none;
  opacity: 0.8;
}

.login-button.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 响应式设计 - 登录页面 */
@media (max-width: 768px) {
  .login-container {
    padding: 15px;
  }

  .login-card {
    padding: 40px 30px;
    margin: 0;
    max-width: 100%;
    border-radius: 20px;
  }

  .login-title {
    font-size: 26px;
    margin-bottom: 40px;
    letter-spacing: 2px;
  }

  .form-group {
    margin-bottom: 25px;
  }

  .form-input {
    padding: 16px 20px;
    font-size: 16px;
  }

  .upload-area {
    padding: 30px 20px;
  }

  .upload-text {
    font-size: 16px;
  }

  .login-button {
    padding: 18px;
    font-size: 16px;
    margin-top: 30px;
  }

  .copyright {
    bottom: 20px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }

  .login-title {
    font-size: 24px;
    margin-bottom: 30px;
  }

  .form-input {
    padding: 14px 18px;
    font-size: 15px;
  }

  .upload-area {
    padding: 25px 15px;
  }
}
