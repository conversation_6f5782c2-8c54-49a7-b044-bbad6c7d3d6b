// 粒子背景效果
function createParticles() {
  const particlesContainer = document.getElementById("particles");
  const particleCount = 50;

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement("div");
    particle.className = "particle";
    particle.style.left = Math.random() * 100 + "%";
    particle.style.animationDelay = Math.random() * 6 + "s";
    particle.style.animationDuration = Math.random() * 3 + 3 + "s";
    particlesContainer.appendChild(particle);
  }
}

// 语音交互功能
let isListening = false;
const voiceOrb = document.getElementById("voiceOrb");
const voiceIcon = document.getElementById("voiceIcon");
const voiceStatus = document.getElementById("voiceStatus");
const visualizer = document.getElementById("visualizer");

voiceOrb.addEventListener("click", function () {
  if (!isListening) {
    startListening();
  } else {
    stopListening();
  }
});

function startListening() {
  isListening = true;
  voiceOrb.classList.add("active");
  voiceIcon.className = "fas fa-stop";
  voiceStatus.textContent = "正在聆听您的指令...";
  visualizer.style.display = "flex";

  // 模拟语音识别过程
  setTimeout(() => {
    voiceStatus.textContent = "正在处理语音指令...";
  }, 2000);

  setTimeout(() => {
    stopListening();
    voiceStatus.textContent = "已识别：请帮我搜索信号设备维护规章";
  }, 4000);
}

function stopListening() {
  isListening = false;
  voiceOrb.classList.remove("active");
  voiceIcon.className = "fas fa-microphone";
  visualizer.style.display = "none";
}

// 模块点击效果
document.querySelectorAll(".module-card").forEach((card) => {
  card.addEventListener("click", function () {
    const module = this.dataset.module;
    // 这里可以添加模块切换逻辑
    console.log("激活模块:", module);

    // 添加激活效果
    this.style.transform = "scale(0.95)";
    setTimeout(() => {
      this.style.transform = "";
    }, 200);
  });
});

// 页面加载完成后的初始化
window.addEventListener("load", function () {
  // 隐藏加载屏幕
  setTimeout(() => {
    document.getElementById("loadingScreen").classList.add("hidden");
  }, 2000);

  // 创建粒子效果
  createParticles();

  // 添加键盘快捷键支持
  document.addEventListener("keydown", function (e) {
    if (e.code === "Space" && e.ctrlKey) {
      e.preventDefault();
      voiceOrb.click();
    }
  });
});

// 响应式语音提示
function updateVoicePrompts() {
  const prompts = [
    "说出您需要的功能模块",
    "AI助手随时为您服务",
    "语音交互让教学更高效",
    "智能识别您的教学需求",
  ];

  let currentPrompt = 0;
  setInterval(() => {
    if (!isListening) {
      voiceStatus.textContent = prompts[currentPrompt];
      currentPrompt = (currentPrompt + 1) % prompts.length;
    }
  }, 3000);
}

// 全息扫描效果
function addHologramScanEffect() {
  const cards = document.querySelectorAll(".module-card");
  cards.forEach((card, index) => {
    setTimeout(() => {
      card.style.opacity = "0";
      card.style.transform = "translateY(50px)";

      setTimeout(() => {
        card.style.transition = "all 0.8s ease";
        card.style.opacity = "1";
        card.style.transform = "translateY(0)";
      }, 100);
    }, index * 200);
  });
}

// AI智能推荐功能
function simulateAIRecommendation() {
  const recommendations = [
    { module: "regulations", text: "检测到您最近关注信号设备，推荐相关规章" },
    { module: "resources", text: "为您推荐最新的教学视频资源" },
    { module: "questions", text: "基于当前课程进度，建议生成配套试题" },
    { module: "planning", text: "智能分析发现您需要更新教学大纲" },
  ];

  setInterval(() => {
    const recommendation =
      recommendations[Math.floor(Math.random() * recommendations.length)];
    showNotification(recommendation.text, recommendation.module);
  }, 15000);
}

// 通知系统
function showNotification(message, type) {
  const notification = document.createElement("div");
  notification.className = "ai-notification";
  notification.innerHTML = `
                <div class="notification-icon">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">AI智能推荐</div>
                    <div class="notification-text">${message}</div>
                </div>
                <div class="notification-close">
                    <i class="fas fa-times"></i>
                </div>
            `;

  // 添加通知样式
  notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 30px;
                background: rgba(0, 0, 0, 0.95);
                border: 1px solid rgba(0, 255, 255, 0.5);
                border-radius: 15px;
                padding: 20px;
                max-width: 350px;
                backdrop-filter: blur(20px);
                z-index: 1001;
                transform: translateX(400px);
                transition: all 0.5s ease;
                box-shadow: 0 10px 40px rgba(0, 255, 255, 0.2);
            `;

  document.body.appendChild(notification);

  // 显示动画
  setTimeout(() => {
    notification.style.transform = "translateX(0)";
  }, 100);

  // 自动消失
  setTimeout(() => {
    notification.style.transform = "translateX(400px)";
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 500);
  }, 5000);

  // 点击关闭
  notification
    .querySelector(".notification-close")
    .addEventListener("click", () => {
      notification.style.transform = "translateX(400px)";
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 500);
    });
}

// 语音命令识别模拟
const voiceCommands = {
  规章检索: "regulations",
  教学资源: "resources",
  随堂出题: "questions",
  备课平台: "planning",
  信号设备: "regulations",
  机车维修: "resources",
  考试题目: "questions",
  教学计划: "planning",
};

function processVoiceCommand(command) {
  const module = voiceCommands[command];
  if (module) {
    activateModule(module);
    voiceStatus.textContent = `已为您激活${command}模块`;
  } else {
    voiceStatus.textContent = "抱歉，未能识别您的指令，请重试";
  }
}

function activateModule(moduleId) {
  const moduleCard = document.querySelector(`[data-module="${moduleId}"]`);
  if (moduleCard) {
    // 高亮激活效果
    moduleCard.style.transform = "scale(1.05)";
    moduleCard.style.boxShadow = "0 0 50px rgba(0, 255, 255, 0.6)";

    setTimeout(() => {
      moduleCard.style.transform = "";
      moduleCard.style.boxShadow = "";
    }, 2000);

    // 这里可以添加实际的模块激活逻辑
    console.log(`激活模块: ${moduleId}`);
  }
}

// 实时状态监控
function updateSystemStatus() {
  const statusIndicators = [
    "AI模型加载完成",
    "语音识别引擎就绪",
    "知识库连接正常",
    "实时同步已启用",
  ];

  let currentStatus = 0;
  const statusElement = document.querySelector(".ai-status span");

  setInterval(() => {
    statusElement.textContent = statusIndicators[currentStatus];
    currentStatus = (currentStatus + 1) % statusIndicators.length;
  }, 4000);
}

// 3D悬浮效果
function add3DFloatEffect() {
  const cards = document.querySelectorAll(".module-card");

  cards.forEach((card) => {
    card.addEventListener("mousemove", (e) => {
      const rect = card.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const rotateX = (y - centerY) / 10;
      const rotateY = (centerX - x) / 10;

      card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
    });

    card.addEventListener("mouseleave", () => {
      card.style.transform = "";
    });
  });
}

// 初始化所有功能
window.addEventListener("load", function () {
  // 隐藏加载屏幕
  setTimeout(() => {
    document.getElementById("loadingScreen").classList.add("hidden");

    // 启动各种效果
    setTimeout(() => {
      addHologramScanEffect();
      updateVoicePrompts();
      simulateAIRecommendation();
      updateSystemStatus();
      add3DFloatEffect();
    }, 500);
  }, 2000);

  // 创建粒子效果
  createParticles();

  // 添加键盘快捷键支持
  document.addEventListener("keydown", function (e) {
    if (e.code === "Space" && e.ctrlKey) {
      e.preventDefault();
      voiceOrb.click();
    }
  });

  // 模拟语音命令测试
  setTimeout(() => {
    if (!isListening) {
      showNotification("系统已准备就绪，您可以开始语音交互", "system");
    }
  }, 5000);
});

// 添加额外的CSS样式用于通知
const additionalStyles = `
            .ai-notification {
                color: #fff;
                font-family: 'Orbitron', monospace;
            }
            
            .notification-icon {
                display: inline-block;
                width: 40px;
                height: 40px;
                background: linear-gradient(45deg, #00ffff, #0080ff);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 15px;
                font-size: 18px;
                float: left;
            }
            
            .notification-content {
                overflow: hidden;
                padding-left: 55px;
            }
            
            .notification-title {
                font-size: 14px;
                font-weight: 700;
                color: #00ffff;
                margin-bottom: 5px;
                text-transform: uppercase;
            }
            
            .notification-text {
                font-size: 12px;
                line-height: 1.4;
                color: #ccc;
            }
            
            .notification-close {
                position: absolute;
                top: 10px;
                right: 10px;
                cursor: pointer;
                color: #666;
                transition: color 0.3s ease;
            }
            
            .notification-close:hover {
                color: #fff;
            }
        `;

const styleSheet = document.createElement("style");
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);
