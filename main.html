<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>铁路智教云 3.0 - 主页</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="prototype.css">
    <link rel="stylesheet" href="main.css">
</head>
<body>
    <!-- 加载屏幕 -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loader"></div>
    </div>

    <!-- 动态背景 -->
    <div class="bg-animation"></div>
    <div class="particles" id="particles"></div>

    <div class="main-container">
        <!-- 左侧区域 -->
        <aside class="left-panel">
            <!-- 课程标题 -->
            <div class="course-header">
                <h1 class="course-title">铁路电务 LKJ 检修指南</h1>
            </div>

            <!-- 用户信息 -->
            <div class="user-profile">
                <div class="user-avatar">
                    <div class="avatar-ring">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <div class="user-name">用户名</div>
            </div>

            <!-- 语音交互 -->
            <div class="voice-interaction">
                <div class="voice-orb-main" id="voiceOrbMain">
                    <div class="voice-icon-main">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <div class="voice-wave-main"></div>
                </div>
                <div class="voice-instruction-main">点击开始语音交互</div>
                <div class="voice-status-main">智能助手准备就绪</div>
            </div>

            <!-- 课程核心词云 -->
            <div class="word-cloud">
                <h3 class="word-cloud-title">
                    <i class="fas fa-train"></i>
                    通信监控主机列车
                </h3>
                <div class="word-cloud-content">
                    <div class="word-cloud-main">LKJ2000</div>
                    <div class="word-cloud-sub">运行监控</div>
                    <div class="word-cloud-sub">检修</div>
                    <div class="word-cloud-sub">信号机</div>
                    <div class="word-cloud-sub">高铁</div>
                    <div class="word-cloud-sub">动车组</div>
                    <div class="word-cloud-sub">通信信号系统</div>
                    <div class="word-cloud-sub">安全控制</div>
                    <div class="word-cloud-sub">速度传感器</div>
                </div>
            </div>
        </aside>

        <!-- 右侧区域 -->
        <main class="right-panel">
            <!-- 动态资源推荐区域 -->
            <section class="resource-carousel">
                <div class="resource-carousel-wrapper">
                    <canvas id="flowCanvas" class="flow-canvas"></canvas>
                    <div class="scroll-container" id="infiniteScroll--left">
                        <!-- 资源卡片将通过JavaScript动态创建 -->
                    </div>
                </div>
            </section>

            <!-- 功能入口卡片区域 -->
            <section class="function-grid">
                <div class="function-card" data-function="resources">
                    <div class="function-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="function-name">资源推荐</div>
                </div>
                <div class="function-card" data-function="query">
                    <div class="function-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="function-name">资料查询</div>
                </div>
                <div class="function-card" data-function="practice">
                    <div class="function-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="function-name">随堂练习</div>
                </div>
                <div class="function-card" data-function="homework">
                    <div class="function-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="function-name">课后小结</div>
                </div>
                <div class="function-card" data-function="answer">
                    <div class="function-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="function-name">答疑解惑</div>
                </div>
                <div class="function-card" data-function="feedback">
                    <div class="function-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="function-name">点名提问</div>
                </div>
                <div class="function-card" data-function="interaction">
                    <div class="function-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="function-name">课堂互动</div>
                </div>
                <div class="function-card" data-function="platform">
                    <div class="function-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="function-name">备课平台</div>
                </div>
            </section>
        </main>
    </div>

    <!-- AI状态指示器 -->
    <div class="ai-status" style="display: none;">
        <div class="ai-indicator">
            <div class="status-dot"></div>
            <span>AI系统运行正常</span>
        </div>
    </div>

    <script src="main.js"></script>
</body>
</html>
