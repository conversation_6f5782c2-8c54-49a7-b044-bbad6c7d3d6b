// main.js - 铁路智教云3.0主页交互脚本

document.addEventListener("DOMContentLoaded", function () {
  // 初始化页面
  initializePage();

  // 初始化水平滚动资源推荐
  initializeHorizontalScroll();

  // 绑定事件监听器
  bindEventListeners();

  // 创建粒子效果
  createParticles();

  // 启动动画
  startAnimations();
});

// 初始化页面
function initializePage() {
  // 隐藏加载屏幕
  setTimeout(() => {
    const loadingScreen = document.getElementById("loadingScreen");
    if (loadingScreen) {
      loadingScreen.style.opacity = "0";
      setTimeout(() => {
        loadingScreen.style.display = "none";
      }, 500);
    }
  }, 1000);

  // 设置用户信息
  const userName = localStorage.getItem("username") || "用户名";
  const userNameElement = document.querySelector(".user-name");
  if (userNameElement) {
    userNameElement.textContent = userName;
  }
}

// 初始化水平滚动资源推荐
function initializeHorizontalScroll() {
  const scrollContainer = document.getElementById("infiniteScroll--left");
  const canvas = document.getElementById("flowCanvas");
  if (!scrollContainer || !canvas) return;

  // 初始化背景流动效果
  initializeBackgroundFlow(canvas);

  // 资源数据
  const resources = [
    {
      name: "LKJ2000系统原理详解",
      meta: "视频教程 • 45分钟",
      category: "video",
      icon: "fas fa-play-circle",
    },
    {
      name: "铁路信号设备检修手册",
      meta: "技术文档 • PDF格式",
      category: "document",
      icon: "fas fa-file-alt",
    },
    {
      name: "列车运行监控仿真",
      meta: "交互仿真 • 在线体验",
      category: "simulation",
      icon: "fas fa-cogs",
    },
    {
      name: "设备故障诊断练习",
      meta: "在线测试 • 20题",
      category: "quiz",
      icon: "fas fa-question-circle",
    },
    {
      name: "高铁动车组维护指南",
      meta: "操作手册 • 实用指导",
      category: "manual",
      icon: "fas fa-book",
    },
    {
      name: "安全控制系统实训",
      meta: "实践练习 • 互动学习",
      category: "practice",
      icon: "fas fa-graduation-cap",
    },
    {
      name: "通信信号系统维护",
      meta: "技术指南 • 专业培训",
      category: "document",
      icon: "fas fa-signal",
    },
    {
      name: "速度传感器校准",
      meta: "操作演示 • 30分钟",
      category: "video",
      icon: "fas fa-tachometer-alt",
    },
  ];

  // 创建资源卡片
  function createResourceCards() {
    scrollContainer.innerHTML = "";

    // 创建两套相同的卡片以实现无缝循环
    const duplicatedResources = [...resources, ...resources];

    duplicatedResources.forEach((resource) => {
      const article = document.createElement("article");
      article.className = "resource-article";
      article.dataset.category = resource.category;

      article.innerHTML = `
        <div class="resource-wrapper">
          <div class="resource-img">
            <i class="${resource.icon}"></i>
          </div>
          <div class="resource-content">
            <div class="resource-title-bar">
              <div class="resource-name">${resource.name}</div>
            </div>
            <div class="resource-meta-bar">
              <div class="resource-meta">${resource.meta}</div>
            </div>
          </div>
        </div>
      `;

      scrollContainer.appendChild(article);

      // 添加点击事件
      article.addEventListener("click", () => {
        handleResourceClick(resource.name, resource.category);
      });
    });
  }

  // 初始化滚动逻辑
  function initializeScrolling() {
    const scrollWidth = scrollContainer.scrollWidth;
    let isScrollingPaused = false;

    function isElementInViewport(el) {
      const rect = el.getBoundingClientRect();
      return rect.right > 0;
    }

    function pauseScrolling() {
      isScrollingPaused = true;
    }

    function resumeScrolling() {
      isScrollingPaused = false;
    }

    // 自动滚动逻辑
    setInterval(() => {
      if (isScrollingPaused) {
        return;
      }

      const first = scrollContainer.querySelector("article");
      if (!isElementInViewport(first)) {
        scrollContainer.appendChild(first);
        scrollContainer.scrollTo(
          scrollContainer.scrollLeft - first.offsetWidth,
          0
        );
      }

      if (scrollContainer.scrollLeft !== scrollWidth) {
        scrollContainer.scrollTo(scrollContainer.scrollLeft + 1, 0);
      }
    }, 20); // 稍微慢一点的滚动速度

    // 为所有卡片添加悬停事件
    const allArticles = scrollContainer.querySelectorAll("article");
    allArticles.forEach((article) => {
      article.addEventListener("mouseenter", pauseScrolling);
      article.addEventListener("mouseleave", resumeScrolling);
    });
  }

  // 初始化
  createResourceCards();

  // 延迟启动滚动，确保DOM完全加载
  setTimeout(() => {
    initializeScrolling();
  }, 100);
}

// 绑定事件监听器
function bindEventListeners() {
  // 语音交互
  const voiceOrb = document.getElementById("voiceOrbMain");
  if (voiceOrb) {
    voiceOrb.addEventListener("click", handleVoiceInteraction);
  }

  // 功能卡片点击
  const functionCards = document.querySelectorAll(".function-card");
  functionCards.forEach((card) => {
    card.addEventListener("click", function () {
      const functionType = this.dataset.function;
      handleFunctionClick(functionType);
    });

    // 添加悬停效果
    card.addEventListener("mouseenter", function () {
      this.style.transform = "translateY(-8px) scale(1.02)";
    });

    card.addEventListener("mouseleave", function () {
      this.style.transform = "translateY(0) scale(1)";
    });
  });

  // 资源卡片点击事件已在动态创建时绑定
}

// 处理语音交互
function handleVoiceInteraction() {
  const voiceOrb = document.getElementById("voiceOrbMain");
  const voiceStatus = document.querySelector(".voice-status-main");
  const voiceInstruction = document.querySelector(".voice-instruction-main");

  if (!voiceOrb || !voiceStatus) return;

  // 切换激活状态
  voiceOrb.classList.toggle("active");

  if (voiceOrb.classList.contains("active")) {
    voiceStatus.textContent = "正在聆听...";
    voiceInstruction.textContent = "请说出您的问题";
    voiceOrb.style.boxShadow = "0 0 40px rgba(0, 255, 255, 0.8)";

    // 模拟语音识别
    setTimeout(() => {
      voiceStatus.textContent = "正在处理...";
      voiceInstruction.textContent = "AI正在思考";

      setTimeout(() => {
        voiceStatus.textContent = "智能助手准备就绪";
        voiceInstruction.textContent = "点击开始语音交互";
        voiceOrb.classList.remove("active");
        voiceOrb.style.boxShadow = "";

        showMessage("语音交互完成", "success");
      }, 2000);
    }, 3000);
  } else {
    voiceStatus.textContent = "智能助手准备就绪";
    voiceInstruction.textContent = "点击开始语音交互";
    voiceOrb.style.boxShadow = "";
  }
}

// 处理功能点击
function handleFunctionClick(functionType) {
  const functionNames = {
    resources: "资源推存",
    query: "资料查询",
    practice: "随堂练习",
    homework: "课后小结",
    answer: "答疑解惑",
    feedback: "点名提问",
    interaction: "课堂互动",
    platform: "备课平台",
  };

  const functionName = functionNames[functionType] || "未知功能";

  // 显示加载效果
  showMessage(`正在打开${functionName}...`, "info");

  // 模拟页面跳转
  setTimeout(() => {
    console.log(`导航到: ${functionType}`);
    // 这里可以添加实际的页面跳转逻辑
    // window.location.href = `${functionType}.html`;
  }, 1000);
}

// 处理资源点击
function handleResourceClick(name, category) {
  const categoryNames = {
    video: "视频教程",
    document: "技术文档",
    simulation: "交互仿真",
    quiz: "在线测试",
  };

  const categoryName = categoryNames[category] || "资源";
  showMessage(`正在加载${categoryName}: ${name}`, "info");

  // 模拟资源加载
  setTimeout(() => {
    console.log(`打开${categoryName}: ${name}`);
    // 这里可以添加实际的资源打开逻辑
    // 根据category类型跳转到不同的页面或模块
  }, 1000);
}

// 创建粒子效果
function createParticles() {
  const particlesContainer = document.getElementById("particles");
  if (!particlesContainer) return;

  const particleCount = 30;

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement("div");
    particle.className = "particle";

    // 随机位置和动画
    particle.style.left = Math.random() * 100 + "%";
    particle.style.animationDelay = Math.random() * 10 + "s";
    particle.style.animationDuration = Math.random() * 5 + 5 + "s";

    particlesContainer.appendChild(particle);
  }
}

// 启动动画
function startAnimations() {
  // 词云动画
  animateWordCloud();

  // 状态指示器动画
  animateStatusIndicator();
}

// 词云动画
function animateWordCloud() {
  const wordCloudSubs = document.querySelectorAll(".word-cloud-sub");

  wordCloudSubs.forEach((word, index) => {
    setTimeout(() => {
      word.style.opacity = "1";
      word.style.transform = "scale(1)";
    }, index * 300);

    // 添加悬停交互
    word.addEventListener("mouseenter", function () {
      this.style.transform = "scale(1.2)";
      this.style.zIndex = "10";
    });

    word.addEventListener("mouseleave", function () {
      this.style.transform = "scale(1)";
      this.style.zIndex = "1";
    });
  });

  // 为词云添加随机浮动效果
  setInterval(() => {
    wordCloudSubs.forEach((word) => {
      const randomX = (Math.random() - 0.5) * 10;
      const randomY = (Math.random() - 0.5) * 10;
      word.style.transform += ` translate(${randomX}px, ${randomY}px)`;

      setTimeout(() => {
        word.style.transform = word.style.transform.replace(
          / translate\([^)]*\)/g,
          ""
        );
      }, 2000);
    });
  }, 5000);
}

// 状态指示器动画
function animateStatusIndicator() {
  const statusDot = document.querySelector(".status-dot");
  if (statusDot) {
    setInterval(() => {
      statusDot.style.background =
        statusDot.style.background === "rgb(0, 255, 128)"
          ? "#00ffff"
          : "#00ff80";
    }, 3000);
  }
}

// 显示消息
function showMessage(message, type = "info") {
  // 创建消息元素
  const messageEl = document.createElement("div");
  messageEl.className = `message message-${type}`;
  messageEl.textContent = message;

  // 样式
  messageEl.style.cssText = `
        position: fixed;
        top: 30px;
        right: 30px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px 25px;
        border-radius: 8px;
        border-left: 4px solid ${
          type === "success"
            ? "#00ff80"
            : type === "error"
            ? "#ff4444"
            : "#00ffff"
        };
        backdrop-filter: blur(10px);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;

  document.body.appendChild(messageEl);

  // 显示动画
  setTimeout(() => {
    messageEl.style.transform = "translateX(0)";
  }, 100);

  // 自动隐藏
  setTimeout(() => {
    messageEl.style.transform = "translateX(100%)";
    setTimeout(() => {
      if (messageEl.parentNode) {
        messageEl.parentNode.removeChild(messageEl);
      }
    }, 300);
  }, 3000);
}

// 键盘快捷键
document.addEventListener("keydown", function (event) {
  // Ctrl + Space 激活语音
  if (event.ctrlKey && event.code === "Space") {
    event.preventDefault();
    const voiceOrb = document.getElementById("voiceOrbMain");
    if (voiceOrb) {
      voiceOrb.click();
    }
  }

  // 数字键快速访问功能
  if (event.key >= "1" && event.key <= "8") {
    const functionCards = document.querySelectorAll(".function-card");
    const index = parseInt(event.key) - 1;
    if (functionCards[index]) {
      functionCards[index].click();
    }
  }
});

// 初始化背景流动效果
function initializeBackgroundFlow(canvas) {
  const ctx = canvas.getContext("2d");
  let w = (canvas.width = canvas.offsetWidth);
  let h = (canvas.height = canvas.offsetHeight);

  const opts = {
    lineCount: 25,
    starCount: 10,
    radVel: 0.002,
    lineBaseVel: 0.02,
    lineAddedVel: 0.02,
    lineBaseLife: 0.15,
    lineAddedLife: 0.003,
    starBaseLife: 12,
    starAddedLife: 12,
    ellipseTilt: -0.15,
    ellipseBaseRadius: 0.06,
    ellipseAddedRadius: 0.008,
    ellipseAxisMultiplierX: 3,
    ellipseAxisMultiplierY: 1,
    ellipseCX: w / 2,
    ellipseCY: h / 2,
    repaintAlpha: 0.05,
  };

  let lines = [];
  let stars = [];
  let tick = 0;

  function FlowLine() {
    this.reset();
  }

  FlowLine.prototype.reset = function () {
    this.rad = Math.random() * Math.PI * 2;
    this.len =
      w * (opts.ellipseBaseRadius + Math.random() * opts.ellipseAddedRadius);
    this.lenVel = opts.lineBaseVel + Math.random() * opts.lineAddedVel;

    this.x = this.px = Math.cos(this.rad) * this.len;
    this.y = this.py = Math.sin(this.rad) * this.len;

    this.life = this.originalLife =
      w * (opts.lineBaseLife + Math.random() * opts.lineAddedLife);
    this.alpha = 0.05 + Math.random() * 0.12;
  };

  FlowLine.prototype.step = function () {
    --this.life;
    this.px = this.x;
    this.py = this.y;
    this.rad += opts.radVel;
    this.len -= this.lenVel;
    this.x = Math.cos(this.rad) * this.len;
    this.y = Math.sin(this.rad) * this.len;
    if (this.life <= 0) this.reset();
  };

  FlowLine.prototype.draw = function () {
    const ratio = Math.abs(this.life / this.originalLife - 0.5);
    ctx.lineWidth = ratio * 1;
    ctx.strokeStyle = `hsla(${
      tick +
      (this.x / (w * (opts.ellipseBaseRadius + opts.ellipseAddedRadius))) * 100
    }, 45%, ${35 - ratio * 60}%, ${this.alpha * 0.4})`;
    ctx.beginPath();
    ctx.moveTo(this.px, this.py);
    ctx.lineTo(this.x, this.y);
    ctx.stroke();
  };

  function FlowStar() {
    this.reset();
  }

  FlowStar.prototype.reset = function () {
    this.x = Math.random() * w;
    this.y = Math.random() * h;
    this.life = opts.starBaseLife + Math.random() * opts.starAddedLife;
  };

  FlowStar.prototype.step = function () {
    --this.life;
    if (this.life <= 0) this.reset();
  };

  FlowStar.prototype.draw = function () {
    ctx.fillStyle = `hsla(${tick + (this.x / w) * 100}, 35%, 25%, 0.08)`;
    ctx.fillRect(this.x, this.y, 1, 1);
  };

  function step() {
    tick += 0.1;

    if (lines.length < opts.lineCount && Math.random() < 0.2) {
      lines.push(new FlowLine());
    }

    if (stars.length < opts.starCount) {
      stars.push(new FlowStar());
    }

    lines.forEach((line) => line.step());
    stars.forEach((star) => star.step());
  }

  function draw() {
    ctx.globalCompositeOperation = "source-over";
    ctx.fillStyle = `rgba(0,0,0,0)`;
    ctx.fillRect(0, 0, w, h);

    ctx.globalCompositeOperation = "lighter";

    ctx.save();
    ctx.translate(opts.ellipseCX, opts.ellipseCY);
    ctx.rotate(opts.ellipseTilt);
    ctx.scale(opts.ellipseAxisMultiplierX, opts.ellipseAxisMultiplierY);

    lines.forEach((line) => line.draw());

    ctx.restore();

    stars.forEach((star) => star.draw());
  }

  function loop() {
    requestAnimationFrame(loop);
    step();
    draw();
  }

  // 窗口大小改变时重新初始化
  window.addEventListener("resize", function () {
    w = canvas.width = canvas.offsetWidth;
    h = canvas.height = canvas.offsetHeight;
    opts.ellipseCX = w / 2;
    opts.ellipseCY = h / 2;
  });

  // 初始化背景
  ctx.fillStyle = "transparent";
  ctx.clearRect(0, 0, w, h);

  loop();
}

// 窗口大小改变时重新初始化轮播
window.addEventListener("resize", function () {
  setTimeout(initializeHorizontalScroll, 100);
});
