<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>铁路智教云 - 未来AI教学系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="prototype.css">
</head>
<body>
    <!-- 加载屏幕 -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loader"></div>
    </div>

    <!-- 动态背景 -->
    <div class="bg-animation"></div>
    <div class="particles" id="particles"></div>

    <div class="container">
        <!-- HUD风格头部 -->
        <header class="header hologram-effect">
            <div class="logo">
                <div class="logo-hologram">
                    <i class="fas fa-train"></i>
                </div>
                <div class="logo-text">
                    <h1>铁路智教云 3.0</h1>
                    <p>AI-Powered Railway Education System</p>
                </div>
            </div>
            <!--
            <div class="user-hud">
                <div>
                    <div style="color: #00ffff; font-size: 16px;">张教授</div>
                    <div style="color: #888; font-size: 12px;">高级权限 | 在线</div>
                </div>
                <div class="user-avatar-holo">
                    <i class="fas fa-user"></i>
                </div>
            </div>  
            -->
        </header>

        <!-- 语音交互中心 -->
        <section class="voice-center">
            <div class="voice-instruction">点击开始语音交互</div>
            <div class="voice-orb" id="voiceOrb">
                <div class="voice-icon">
                    <i class="fas fa-microphone" id="voiceIcon"></i>
                </div>
                <div class="voice-wave"></div>
            </div>
            <div class="voice-status" id="voiceStatus">AI助手准备就绪</div>
            
            <!-- 语音可视化器 -->
            <div class="voice-visualizer" id="visualizer" style="display: none;">
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
            </div>
        </section>

        <!-- 全息功能模块 -->
        <section class="modules-grid">
            <!-- 规章检索模块 -->
            <div class="module-card module-regulations hologram-effect" data-module="regulations">
                <div class="module-icon">
                    <i class="fas fa-search-location"></i>
                </div>
                <h3 class="module-title" style="color: #00ffff;">智能规章检索</h3>
                <p class="module-description">
                    运用自然语言处理技术，精准检索铁路行业规章制度，确保教学内容严谨准确
                </p>
                <ul class="module-features">
                    <li>语义理解搜索</li>
                    <li>条文精准定位</li>
                    <li>实时更新同步</li>
                    <li>智能推荐关联</li>
                </ul>
            </div>

            <!-- 教学资源模块 -->
            <div class="module-card module-resources hologram-effect" data-module="resources">
                <div class="module-icon">
                    <i class="fas fa-cube"></i>
                </div>
                <h3 class="module-title" style="color: #ff6b35;">多媒体资源库</h3>
                <p class="module-description">
                    AI驱动的智能资源匹配系统，根据教学上下文自动推荐最适合的多媒体素材
                </p>
                <ul class="module-features">
                    <li>上下文智能匹配</li>
                    <li>多格式资源支持</li>
                    <li>实时内容分析</li>
                    <li>个性化推荐引擎</li>
                </ul>
            </div>

            <!-- 随堂出题模块 -->
            <div class="module-card module-questions hologram-effect" data-module="questions">
                <div class="module-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 class="module-title" style="color: #8000ff;">智能出题引擎</h3>
                <p class="module-description">
                    基于深度学习的题目生成算法，创造富有挑战性和互动性的专业试题
                </p>
                <ul class="module-features">
                    <li>动态难度调节</li>
                    <li>多维度题型生成</li>
                    <li>实时反馈分析</li>
                    <li>学习轨迹追踪</li>
                </ul>
            </div>

            <!-- 备课平台模块 -->
            <div class="module-card module-planning hologram-effect" data-module="planning">
                <div class="module-icon">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <h3 class="module-title" style="color: #00ff80;">智慧备课助手</h3>
                <p class="module-description">
                    AI辅助教学设计，自动生成结构化教学大纲、教案和讲义，提升备课效率
                </p>
                <ul class="module-features">
                    <li>知识图谱构建</li>
                    <li>教学路径规划</li>
                    <li>内容自动生成</li>
                    <li>教学效果预测</li>
                </ul>
            </div>
        </section>
    </div>

    <!-- AI状态指示器 -->
    <div class="ai-status">
        <div class="ai-indicator">
            <div class="status-dot"></div>
            <span>AI系统运行正常</span>
        </div>
    </div>
    <script src="prototype.js"></script>
</body>
</html>
