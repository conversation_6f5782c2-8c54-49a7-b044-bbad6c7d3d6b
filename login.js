// login.js - 铁路智教云3.0登录页面交互脚本

document.addEventListener("DOMContentLoaded", function () {
  // 初始化页面
  initializePage();

  // 绑定事件监听器
  bindEventListeners();

  // 创建粒子效果
  createParticles();

  // 设置实时验证
  setupRealTimeValidation();
});

// 初始化页面
function initializePage() {
  // 隐藏加载屏幕
  setTimeout(() => {
    const loadingScreen = document.getElementById("loadingScreen");
    if (loadingScreen) {
      loadingScreen.classList.add("hidden");
    }
  }, 1000);
}

// 绑定事件监听器
function bindEventListeners() {
  // 登录表单提交
  const loginForm = document.getElementById("loginForm");
  if (loginForm) {
    loginForm.addEventListener("submit", handleLogin);
  }

  // 文件上传处理
  const fileInput = document.getElementById("fileInput");
  if (fileInput) {
    fileInput.addEventListener("change", handleFileUpload);
  }

  // 输入框焦点效果
  const inputs = document.querySelectorAll(".form-input");
  inputs.forEach((input) => {
    input.addEventListener("focus", handleInputFocus);
    input.addEventListener("blur", handleInputBlur);
  });
}

// 处理登录表单提交
function handleLogin(event) {
  event.preventDefault();

  const username = document.getElementById("username").value.trim();
  const password = document.getElementById("password").value.trim();
  const courseIntro = document.getElementById("courseIntro").value.trim();

  // 重置验证状态
  clearValidationStates();

  // 表单验证
  let isValid = true;

  if (!username) {
    setInputState("username", "error");
    showMessage("请输入用户名", "error");
    isValid = false;
  } else if (username.length < 3) {
    setInputState("username", "error");
    showMessage("用户名至少需要3个字符", "error");
    isValid = false;
  } else {
    setInputState("username", "success");
  }

  if (!password) {
    setInputState("password", "error");
    showMessage("请输入密码", "error");
    isValid = false;
  } else if (password.length < 6) {
    setInputState("password", "error");
    showMessage("密码至少需要6个字符", "error");
    isValid = false;
  } else {
    setInputState("password", "success");
  }

  if (!isValid) return;

  // 显示登录中状态
  const loginButton = document.querySelector(".login-button");
  const originalText = loginButton.innerHTML;
  loginButton.classList.add("loading");
  loginButton.innerHTML = "登录中...";
  loginButton.disabled = true;

  // 模拟登录过程
  setTimeout(() => {
    // 这里可以添加实际的登录逻辑
    console.log("登录信息:", { username, password, courseIntro });

    // 登录成功，跳转到主页面
    showMessage("登录成功！正在跳转...", "success");

    // 保存用户信息到localStorage
    localStorage.setItem("username", username);
    localStorage.setItem("loginTime", new Date().toISOString());

    setTimeout(() => {
      window.location.href = "main.html";
    }, 1500);
  }, 2000);
}

// 处理文件上传
function handleFileUpload(event) {
  const files = event.target.files;
  const uploadArea = document.querySelector(".upload-area");

  if (files.length > 0) {
    let fileNames = [];
    for (let i = 0; i < Math.min(files.length, 3); i++) {
      fileNames.push(files[i].name);
    }

    const displayText = fileNames.join(", ");
    const moreText = files.length > 3 ? ` 等${files.length}个文件` : "";

    uploadArea.innerHTML = `
            <div class="upload-text" style="color: #00ff80;">已选择文件</div>
            <div class="upload-hint">${displayText}${moreText}</div>
        `;

    showMessage(`成功选择 ${files.length} 个文件`, "success");
  }
}

// 输入框焦点效果
function handleInputFocus(event) {
  const input = event.target;
  input.style.transform = "scale(1.02)";
}

function handleInputBlur(event) {
  const input = event.target;
  input.style.transform = "scale(1)";
}

// 显示消息提示
function showMessage(message, type = "info") {
  // 移除现有消息
  const existingMessage = document.querySelector(".message-toast");
  if (existingMessage) {
    existingMessage.remove();
  }

  // 创建消息元素
  const messageElement = document.createElement("div");
  messageElement.className = `message-toast ${type}`;
  messageElement.textContent = message;

  // 添加样式
  messageElement.style.cssText = `
        position: fixed;
        top: 30px;
        right: 30px;
        padding: 15px 25px;
        border-radius: 10px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        backdrop-filter: blur(20px);
    `;

  // 根据类型设置颜色
  switch (type) {
    case "success":
      messageElement.style.background =
        "linear-gradient(45deg, #00ff80, #00ff40)";
      messageElement.style.border = "1px solid rgba(0, 255, 128, 0.5)";
      break;
    case "error":
      messageElement.style.background =
        "linear-gradient(45deg, #ff4040, #ff0000)";
      messageElement.style.border = "1px solid rgba(255, 64, 64, 0.5)";
      break;
    default:
      messageElement.style.background =
        "linear-gradient(45deg, #00ffff, #0080ff)";
      messageElement.style.border = "1px solid rgba(0, 255, 255, 0.5)";
  }

  document.body.appendChild(messageElement);

  // 显示动画
  setTimeout(() => {
    messageElement.style.opacity = "1";
    messageElement.style.transform = "translateX(0)";
  }, 100);

  // 自动隐藏
  setTimeout(() => {
    messageElement.style.opacity = "0";
    messageElement.style.transform = "translateX(100%)";
    setTimeout(() => {
      if (messageElement.parentNode) {
        messageElement.parentNode.removeChild(messageElement);
      }
    }, 300);
  }, 3000);
}

// 创建粒子效果
function createParticles() {
  const particlesContainer = document.getElementById("particles");
  if (!particlesContainer) return;

  const particleCount = 50;

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement("div");
    particle.className = "particle";

    // 随机位置和动画延迟
    particle.style.left = Math.random() * 100 + "%";
    particle.style.animationDelay = Math.random() * 6 + "s";
    particle.style.animationDuration = Math.random() * 3 + 3 + "s";

    particlesContainer.appendChild(particle);
  }
}

// 设置输入框验证状态
function setInputState(inputId, state) {
  const input = document.getElementById(inputId);
  if (input) {
    input.classList.remove("error", "success");
    if (state) {
      input.classList.add(state);
    }
  }
}

// 清除所有验证状态
function clearValidationStates() {
  const inputs = document.querySelectorAll(".form-input");
  inputs.forEach((input) => {
    input.classList.remove("error", "success");
  });
}

// 实时验证
function setupRealTimeValidation() {
  const usernameInput = document.getElementById("username");
  const passwordInput = document.getElementById("password");

  if (usernameInput) {
    usernameInput.addEventListener("input", function () {
      const value = this.value.trim();
      if (value.length === 0) {
        setInputState("username", "");
      } else if (value.length < 3) {
        setInputState("username", "error");
      } else {
        setInputState("username", "success");
      }
    });
  }

  if (passwordInput) {
    passwordInput.addEventListener("input", function () {
      const value = this.value.trim();
      if (value.length === 0) {
        setInputState("password", "");
      } else if (value.length < 6) {
        setInputState("password", "error");
      } else {
        setInputState("password", "success");
      }
    });
  }
}

// 键盘快捷键支持
document.addEventListener("keydown", function (event) {
  // Enter键快速登录
  if (event.key === "Enter" && event.ctrlKey) {
    const loginButton = document.querySelector(".login-button");
    if (loginButton && !loginButton.disabled) {
      loginButton.click();
    }
  }
});
