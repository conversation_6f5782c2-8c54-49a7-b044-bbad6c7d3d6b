/* 主页样式 - 铁路智教云 3.0 */

/* 主页背景覆盖 */
body {
  background: linear-gradient(
    135deg,
    #0a1428 0%,
    #1a2a4a 25%,
    #2a3a5a 50%,
    #1a2a4a 75%,
    #0a1428 100%
  ) !important;
  margin: 0;
  padding: 0;
  font-family: "Arial", sans-serif;
  overflow: hidden;
}

/* 基础布局 */
.main-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* 底部科技渐变背景 */
.main-container::after {
  content: "";
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(
    0deg,
    rgba(0, 255, 255, 0.1) 0%,
    rgba(128, 0, 255, 0.08) 30%,
    rgba(255, 0, 128, 0.06) 60%,
    transparent 100%
  );
  z-index: -1;
  animation: bottomGradientFlow 8s ease-in-out infinite;
}

@keyframes bottomGradientFlow {
  0%,
  100% {
    background: linear-gradient(
      0deg,
      rgba(0, 255, 255, 0.1) 0%,
      rgba(128, 0, 255, 0.08) 30%,
      rgba(255, 0, 128, 0.06) 60%,
      transparent 100%
    );
  }
  33% {
    background: linear-gradient(
      0deg,
      rgba(128, 0, 255, 0.1) 0%,
      rgba(255, 0, 128, 0.08) 30%,
      rgba(0, 255, 255, 0.06) 60%,
      transparent 100%
    );
  }
  66% {
    background: linear-gradient(
      0deg,
      rgba(255, 0, 128, 0.1) 0%,
      rgba(0, 255, 255, 0.08) 30%,
      rgba(128, 0, 255, 0.06) 60%,
      transparent 100%
    );
  }
}

/* 左侧面板 */
.left-panel {
  width: 320px;
  background: transparent;
  border-right: none;
  backdrop-filter: none;
  padding: 30px 25px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  box-shadow: none;
}

/* 课程标题 */
.course-header {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: none;
}

.course-title {
  background: linear-gradient(135deg, #00ffff, #ffffff, #8000ff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
  letter-spacing: 1px;
}

/* 用户信息 */
.user-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.user-avatar {
  position: relative;
}

.avatar-ring {
  width: 80px;
  height: 80px;
  border: 2px solid rgba(0, 255, 255, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  position: relative;
  overflow: hidden;
}

.avatar-ring::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #00ffff, #8000ff, #00ffff);
  z-index: -1;
  animation: rotate 3s linear infinite;
}

.avatar-ring i {
  font-size: 32px;
  color: #00ffff;
}

.user-name {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
}

/* 语音交互 */
.voice-interaction {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 25px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 15px;
  position: relative;
}

.voice-orb-main {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: radial-gradient(
    circle at center,
    rgba(0, 255, 255, 0.3) 0%,
    rgba(0, 255, 255, 0.1) 30%,
    rgba(128, 0, 255, 0.1) 60%,
    transparent 100%
  );
  border: 2px solid rgba(0, 255, 255, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.voice-orb-main::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent,
    rgba(0, 255, 255, 0.1),
    transparent,
    rgba(128, 0, 255, 0.1),
    transparent
  );
  animation: rotate 4s linear infinite;
  z-index: 1;
}

.voice-orb-main:hover {
  transform: scale(1.05);
  box-shadow: 0 0 40px rgba(0, 255, 255, 0.6),
    inset 0 0 30px rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
}

.voice-icon-main {
  font-size: 32px;
  color: #00ffff;
  z-index: 3;
  filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.8));
}

.voice-wave-main {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.2), transparent);
  animation: voiceWaveIdle 3s ease-in-out infinite;
  z-index: 2;
}

@keyframes voiceWaveIdle {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
}

.voice-instruction-main {
  color: #00ffff;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.voice-status-main {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  text-align: center;
}

/* 词云 */
.word-cloud {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 15px;
  padding: 25px;
  flex: 1;
  position: relative;
  overflow: hidden;
}

.word-cloud::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 255, 255, 0.05) 0%,
    rgba(128, 0, 255, 0.05) 50%,
    transparent 100%
  );
  z-index: 1;
}

.word-cloud-title {
  color: #00ffff;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  z-index: 2;
}

.word-cloud-content {
  position: relative;
  width: 100%;
  height: 200px;
  z-index: 2;
}

.word-cloud-main {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 28px;
  font-weight: 900;
  background: linear-gradient(135deg, #00ffff, #ff00ff, #ffff00);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  letter-spacing: 2px;
  animation: wordPulse 3s ease-in-out infinite;
}

.word-cloud-sub {
  position: absolute;
  font-size: 16px;
  font-weight: 500;
  opacity: 0.8;
  transition: all 0.3s ease;
  cursor: pointer;
}

.word-cloud-sub:nth-child(2) {
  top: 15%;
  left: 10%;
  color: #ff6b6b;
  text-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
}

.word-cloud-sub:nth-child(3) {
  top: 20%;
  right: 8%;
  color: #4ecdc4;
  text-shadow: 0 0 10px rgba(78, 205, 196, 0.5);
}

.word-cloud-sub:nth-child(4) {
  top: 65%;
  left: 5%;
  color: #45b7d1;
  text-shadow: 0 0 10px rgba(69, 183, 209, 0.5);
}

.word-cloud-sub:nth-child(5) {
  bottom: 15%;
  right: 12%;
  color: #f9ca24;
  text-shadow: 0 0 10px rgba(249, 202, 36, 0.5);
}

.word-cloud-sub:nth-child(6) {
  top: 75%;
  left: 45%;
  color: #a29bfe;
  text-shadow: 0 0 10px rgba(162, 155, 254, 0.5);
}

.word-cloud-sub:nth-child(7) {
  top: 35%;
  left: 70%;
  color: #fd79a8;
  text-shadow: 0 0 10px rgba(253, 121, 168, 0.5);
}

.word-cloud-sub:nth-child(8) {
  bottom: 35%;
  left: 65%;
  color: #00b894;
  text-shadow: 0 0 10px rgba(0, 184, 148, 0.5);
}

.word-cloud-sub:nth-child(9) {
  top: 45%;
  left: 25%;
  color: #e17055;
  text-shadow: 0 0 10px rgba(225, 112, 85, 0.5);
}

.word-cloud-sub:hover {
  transform: scale(1.2);
  opacity: 1;
  filter: brightness(1.3);
}

@keyframes wordPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 30px;
  gap: 30px;
  min-width: 0;
  overflow: hidden;
}

/* 资源推荐区域 */
.resource-carousel {
  height: 75%;
  background: transparent;
  position: relative;
  overflow: hidden;
}

.resource-carousel-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.flow-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 16px;
}

/* 水平滚动容器 */
.scroll-container {
  width: 100%;
  height: 100%;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  display: flex;
  align-items: center;
  padding: 20px 0;
  -ms-overflow-style: none;
  scrollbar-width: none;
  position: relative;
  z-index: 2;
}

.scroll-container::-webkit-scrollbar {
  display: none;
}

/* 资源卡片 */
.resource-article {
  min-width: 320px;
  height: 280px;
  padding: 15px;
  display: inline-block;
  white-space: normal;
}

.resource-wrapper {
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15px);
  height: 100%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.resource-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.08),
    transparent
  );
  transition: left 0.5s;
}

.resource-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(0, 0, 0, 0.5);
}

.resource-wrapper:hover::before {
  left: 100%;
}

/* 资源图标区域 */
.resource-img {
  height: 50%;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 255, 0.2),
    rgba(128, 0, 255, 0.2)
  );
  border-radius: 15px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 48px;
  color: #00ffff;
  margin-bottom: 15px;
  position: relative;
  overflow: hidden;
}

.resource-img::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent,
    rgba(0, 255, 255, 0.1),
    transparent,
    rgba(128, 0, 255, 0.1),
    transparent
  );
  animation: rotate 6s linear infinite;
}

.resource-img i {
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 0 15px rgba(0, 255, 255, 0.8));
}

/* 不同类型的图标颜色 */
.resource-article[data-category="video"] .resource-img {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 107, 0.3),
    rgba(255, 142, 142, 0.2)
  );
}

.resource-article[data-category="video"] .resource-img i {
  color: #ff6b6b;
  filter: drop-shadow(0 0 15px rgba(255, 107, 107, 0.8));
}

.resource-article[data-category="document"] .resource-img {
  background: linear-gradient(
    135deg,
    rgba(78, 205, 196, 0.3),
    rgba(126, 214, 204, 0.2)
  );
}

.resource-article[data-category="document"] .resource-img i {
  color: #4ecdc4;
  filter: drop-shadow(0 0 15px rgba(78, 205, 196, 0.8));
}

.resource-article[data-category="simulation"] .resource-img {
  background: linear-gradient(
    135deg,
    rgba(69, 183, 209, 0.3),
    rgba(115, 197, 218, 0.2)
  );
}

.resource-article[data-category="simulation"] .resource-img i {
  color: #45b7d1;
  filter: drop-shadow(0 0 15px rgba(69, 183, 209, 0.8));
}

.resource-article[data-category="quiz"] .resource-img {
  background: linear-gradient(
    135deg,
    rgba(249, 202, 36, 0.3),
    rgba(253, 216, 53, 0.2)
  );
}

.resource-article[data-category="quiz"] .resource-img i {
  color: #f9ca24;
  filter: drop-shadow(0 0 15px rgba(249, 202, 36, 0.8));
}

.resource-article[data-category="manual"] .resource-img {
  background: linear-gradient(
    135deg,
    rgba(253, 121, 168, 0.3),
    rgba(255, 159, 243, 0.2)
  );
}

.resource-article[data-category="manual"] .resource-img i {
  color: #fd79a8;
  filter: drop-shadow(0 0 15px rgba(253, 121, 168, 0.8));
}

.resource-article[data-category="practice"] .resource-img {
  background: linear-gradient(
    135deg,
    rgba(162, 155, 254, 0.3),
    rgba(199, 184, 255, 0.2)
  );
}

.resource-article[data-category="practice"] .resource-img i {
  color: #a29bfe;
  filter: drop-shadow(0 0 15px rgba(162, 155, 254, 0.8));
}

/* 资源内容区域 */
.resource-content {
  height: 50%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.resource-title-bar {
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  overflow: hidden;
}

.resource-name {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resource-meta-bar {
  height: 60px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.resource-meta {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
}

/* 功能网格 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  height: 25%;
  flex-shrink: 0;
}

.function-card {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 16px 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.function-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.function-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(0, 0, 0, 0.4);
}

.function-card:hover::before {
  left: 100%;
}

/* 为每个功能卡片设置不同的图标颜色 */
.function-card[data-function="resources"] .function-icon {
  color: #ff6b6b;
  filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.6));
}

.function-card[data-function="query"] .function-icon {
  color: #4ecdc4;
  filter: drop-shadow(0 0 10px rgba(78, 205, 196, 0.6));
}

.function-card[data-function="practice"] .function-icon {
  color: #45b7d1;
  filter: drop-shadow(0 0 10px rgba(69, 183, 209, 0.6));
}

.function-card[data-function="homework"] .function-icon {
  color: #f9ca24;
  filter: drop-shadow(0 0 10px rgba(249, 202, 36, 0.6));
}

.function-card[data-function="answer"] .function-icon {
  color: #f0932b;
  filter: drop-shadow(0 0 10px rgba(240, 147, 43, 0.6));
}

.function-card[data-function="feedback"] .function-icon {
  color: #eb4d4b;
  filter: drop-shadow(0 0 10px rgba(235, 77, 75, 0.6));
}

.function-card[data-function="interaction"] .function-icon {
  color: #6c5ce7;
  filter: drop-shadow(0 0 10px rgba(108, 92, 231, 0.6));
}

.function-card[data-function="platform"] .function-icon {
  color: #a29bfe;
  filter: drop-shadow(0 0 10px rgba(162, 155, 254, 0.6));
}

.function-icon {
  font-size: 32px;
  transition: all 0.3s ease;
}

.function-card:hover .function-icon {
  transform: scale(1.2) rotateY(360deg);
  filter: brightness(1.3) saturate(1.2);
}

.function-name {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
}

/* AI状态指示器 */
.ai-status {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.ai-indicator {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 25px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #00ff80;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.ai-indicator span {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

/* 语音激活状态 */
.voice-orb-main.active {
  background: radial-gradient(
    circle,
    rgba(0, 255, 255, 0.4),
    rgba(0, 255, 255, 0.1)
  );
  border-color: #00ffff;
  animation: voicePulse 1s infinite;
}

.voice-orb-main.active .voice-wave-main {
  animation: voiceWave 0.8s infinite;
}

@keyframes voicePulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes voiceWave {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 词云子项初始状态 */
.word-cloud-sub {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

/* 加载屏幕 */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #000000, #1a1a3a, #2a1a4a);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  transition: opacity 0.5s ease;
}

.loader {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-top: 3px solid #00ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 消息样式增强 */
.message {
  font-family: "Arial", sans-serif;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.message-success {
  border-left-color: #00ff80;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 128, 0.1),
    rgba(0, 0, 0, 0.9)
  );
}

.message-error {
  border-left-color: #ff4444;
  background: linear-gradient(
    135deg,
    rgba(255, 68, 68, 0.1),
    rgba(0, 0, 0, 0.9)
  );
}

.message-info {
  border-left-color: #00ffff;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 255, 0.1),
    rgba(0, 0, 0, 0.9)
  );
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 280px;
  }

  .function-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .resource-article {
    min-width: 280px;
    height: 240px;
    padding: 12px;
  }

  .resource-img {
    font-size: 36px;
  }

  .resource-name {
    font-size: 14px;
  }

  .resource-meta {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    height: auto;
    flex-direction: row;
    gap: 15px;
    padding: 15px;
    overflow-x: auto;
  }

  .left-panel > * {
    flex-shrink: 0;
  }

  .course-header {
    min-width: 200px;
  }

  .user-profile {
    min-width: 120px;
  }

  .voice-interaction {
    min-width: 180px;
    padding: 15px;
  }

  .voice-orb-main {
    width: 80px;
    height: 80px;
  }

  .voice-icon-main {
    font-size: 24px;
  }

  .word-cloud {
    min-width: 200px;
  }

  .right-panel {
    padding: 15px;
    gap: 20px;
    width: 100%;
    overflow: hidden;
  }

  .function-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .function-card {
    padding: 15px;
  }

  .function-icon {
    font-size: 24px;
  }

  .resource-carousel {
    height: 50%;
  }

  .resource-carousel-wrapper {
    width: 100%;
    overflow: hidden;
  }

  .carousel-item {
    width: 200px;
    height: 120px;
  }

  .ai-status {
    bottom: 15px;
    right: 15px;
  }
}

@media (max-width: 480px) {
  .left-panel {
    padding: 10px;
    gap: 10px;
  }

  .right-panel {
    padding: 10px;
    width: 100%;
    overflow: hidden;
  }

  .function-grid {
    grid-template-columns: 1fr 1fr;
    gap: 10px;
  }

  .function-card {
    padding: 12px;
  }

  .function-icon {
    font-size: 20px;
  }

  .function-name {
    font-size: 12px;
  }

  .resource-carousel {
    height: 50%;
  }

  .resource-carousel-wrapper {
    width: 100%;
    overflow: hidden;
  }
}
