<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>灰犬智慧城市中心驾驶舱</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: grid;
            grid-template-columns: 250px 1fr 300px;
            grid-template-rows: 60px 1fr 80px;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        /* Header */
        .header {
            grid-column: 1 / -1;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 18px;
            font-weight: normal;
        }

        .header-nav {
            display: flex;
            gap: 30px;
        }

        .header-nav span {
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.3s;
        }

        .header-nav span:hover {
            opacity: 1;
        }

        .weather-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Left Sidebar - AI Assistant */
        .left-sidebar {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .ai-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #00d4aa 0%, #00a8ff 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            position: relative;
        }

        .ai-avatar::before {
            content: '';
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
            position: absolute;
            left: 18px;
            top: 20px;
        }

        .ai-avatar::after {
            content: '';
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
            position: absolute;
            right: 18px;
            top: 20px;
        }

        .ai-greeting {
            font-size: 14px;
            margin-bottom: 20px;
        }

        .chat-messages {
            font-size: 12px;
            line-height: 1.6;
            opacity: 0.8;
        }

        .chat-messages p {
            margin-bottom: 10px;
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            padding: 10px;
        }

        .card {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .card h3 {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .card p {
            font-size: 12px;
            opacity: 0.7;
            margin-bottom: 15px;
        }

        .card-button {
            background: #00a8ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .card-button:hover {
            background: #0097e6;
        }

        .card-button.green {
            background: #00d4aa;
        }

        .card-button.green:hover {
            background: #00c7a0;
        }

        /* Weather Card */
        .weather-card {
            grid-column: 1 / -1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
        }

        .weather-temp {
            font-size: 48px;
            font-weight: 300;
        }

        .weather-details {
            font-size: 14px;
            opacity: 0.8;
        }

        .weather-chart {
            flex: 1;
            margin: 0 20px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            position: relative;
        }

        .weather-chart::before {
            content: '';
            position: absolute;
            left: 20%;
            top: 50%;
            width: 60%;
            height: 2px;
            background: linear-gradient(90deg, #00d4aa 0%, #00a8ff 50%, #667eea 100%);
            border-radius: 1px;
        }

        /* Right Sidebar */
        .right-sidebar {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .music-player {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 15px;
            backdrop-filter: blur(10px);
        }

        .music-cover {
            width: 100%;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            margin-bottom: 10px;
            position: relative;
            display: flex;
            align-items: flex-end;
            padding: 10px;
        }

        .music-title {
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .music-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 10px;
        }

        .control-btn {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .call-interface {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            text-align: center;
        }

        .call-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f0f0f0 0%, #d0d0d0 100%);
            margin: 0 auto 15px;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="35" r="15" fill="%23666"/><path d="M25 75 Q25 60 50 60 Q75 60 75 75 Z" fill="%23666"/></svg>');
            background-size: cover;
        }

        .call-name {
            font-size: 16px;
            margin-bottom: 15px;
        }

        .call-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .call-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .call-btn.accept {
            background: #00d4aa;
            color: white;
        }

        .call-btn.decline {
            background: #ff4757;
            color: white;
        }

        /* Bottom Navigation */
        .bottom-nav {
            grid-column: 1 / -1;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 50px;
            backdrop-filter: blur(10px);
        }

        .nav-item {
            text-align: center;
            cursor: pointer;
            padding: 10px 20px;
            border-radius: 10px;
            transition: background 0.3s;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .nav-item.active {
            background: rgba(0, 168, 255, 0.2);
        }

        .nav-item span {
            display: block;
            font-size: 12px;
            margin-top: 5px;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>灰犬智慧城市中心驾驶舱</h1>
            <div class="header-nav">
                <span>数据总览</span>
                <span>数据档案</span>
                <span>数据监督</span>
                <span>数据总览</span>
                <span>数据档案</span>
            </div>
            <div class="weather-info">
                <span>🌤️ 晴转多云</span>
                <span>19°C/26°C</span>
            </div>
        </div>

        <!-- Left Sidebar - AI Assistant -->
        <div class="left-sidebar">
            <div class="ai-avatar"></div>
            <div class="ai-greeting">
                <strong>Hi! 我是你的AI助手</strong>
            </div>
            <div class="chat-messages">
                <p>are you shining just for me?<br>你是否只为我一个人闪耀</p>
                <p>City of stars,<br>星空之城</p>
                <p>there's so much that i can't see<br>命运太大无世界 目不暇接</p>
                <p>Who knows,<br>谁知道呢</p>
                <p>is this the start of something wonderful and new</p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Weather Card -->
            <div class="weather-card">
                <div>
                    <div class="weather-temp">18°</div>
                    <div class="weather-details">
                        <div>22°C</div>
                        <div>14°C</div>
                        <div>雾里看花</div>
                    </div>
                </div>
                <div class="weather-chart"></div>
                <div style="font-size: 12px; opacity: 0.7;">
                    <div>now</div>
                    <div>12:00</div>
                    <div>18:00</div>
                </div>
            </div>

            <!-- Function Cards -->
            <div class="card">
                <div class="card-icon">🚗</div>
                <h3>道路拥堵</h3>
                <p>道路拥堵情况监测 13分钟</p>
                <button class="card-button">切换视角</button>
            </div>

            <div class="card">
                <div class="card-icon">🚙</div>
                <h3>小鹏汽车</h3>
                <p>车辆状态监测 15分钟</p>
                <button class="card-button green">立即导航</button>
            </div>

            <div class="card">
                <div class="card-icon">🚦</div>
                <h3>路况拥堵</h3>
                <p>道路拥堵情况监测 13分钟</p>
                <button class="card-button">切换视角</button>
            </div>

            <div class="card">
                <div class="card-icon">🚘</div>
                <h3>小鹏汽车</h3>
                <p>车辆状态监测 15分钟</p>
                <button class="card-button green">立即导航</button>
            </div>
        </div>

        <!-- Right Sidebar -->
        <div class="right-sidebar">
            <!-- Music Player -->
            <div class="music-player">
                <div class="music-cover">
                    <div class="music-title">Five More Hours</div>
                </div>
                <div class="music-controls">
                    <button class="control-btn">⏸️</button>
                    <button class="control-btn">⏭️</button>
                </div>
            </div>

            <!-- Call Interface -->
            <div class="call-interface">
                <div class="call-avatar"></div>
                <div class="call-name">白教授</div>
                <div class="call-actions">
                    <button class="call-btn accept">📞</button>
                    <button class="call-btn decline">📞</button>
                </div>
            </div>

            <!-- Weather Info -->
            <div class="card">
                <div style="text-align: center;">
                    <div style="font-size: 36px; margin-bottom: 10px;">18°</div>
                    <div style="font-size: 12px; opacity: 0.7;">
                        <div>🌡️ 22°C</div>
                        <div>🌡️ 14°C</div>
                        <div>雾里看花</div>
                        <div style="margin-top: 10px; color: #00d4aa;">微风</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item">
                <div class="nav-icon">📊</div>
                <span>资源推荐</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📋</div>
                <span>资料查询</span>
            </div>
            <div class="nav-item active">
                <div class="nav-icon">🚗</div>
                <span>随堂练习</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📝</div>
                <span>课后小结</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">❓</div>
                <span>点名提问</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">🎯</div>
                <span>课堂互动</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">⚖️</div>
                <span>备课平台</span>
            </div>
        </div>
    </div>
</body>
</html>