<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>灰犬智慧城市中心驾驶舱</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2d3748 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: grid;
            grid-template-columns: 240px 1fr 320px;
            grid-template-rows: 70px 1fr 60px;
            height: 100vh;
            gap: 15px;
            padding: 15px;
        }

        /* Header */
        .header {
            grid-column: 1 / -1;
            background: linear-gradient(90deg, rgba(15, 20, 25, 0.8) 0%, rgba(26, 35, 50, 0.6) 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 20px;
            font-weight: 500;
            color: #00d4aa;
            text-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
        }

        .header-nav {
            display: flex;
            gap: 40px;
        }

        .header-nav span {
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.3s;
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 6px;
        }

        .header-nav span:hover {
            opacity: 1;
            background: rgba(0, 212, 170, 0.1);
        }

        .weather-info {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
        }

        .weather-info span:first-child {
            color: #ffd700;
        }

        /* Left Sidebar - AI Assistant */
        .left-sidebar {
            background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.7) 100%);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
        }

        .ai-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00d4aa 0%, #00a8ff 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
            box-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
            align-self: center;
        }

        .ai-avatar::before {
            content: '';
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            position: absolute;
            left: 22px;
            top: 25px;
        }

        .ai-avatar::after {
            content: '';
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            position: absolute;
            right: 22px;
            top: 25px;
        }

        .ai-greeting {
            font-size: 16px;
            margin-bottom: 25px;
            text-align: center;
            color: #00d4aa;
        }

        .chat-messages {
            font-size: 13px;
            line-height: 1.8;
            opacity: 0.9;
            flex: 1;
        }

        .chat-messages p {
            margin-bottom: 15px;
            padding: 8px 0;
            border-left: 2px solid rgba(0, 212, 170, 0.3);
            padding-left: 12px;
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 0;
        }

        .main-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .main-title {
            font-size: 24px;
            font-weight: 500;
            color: #00d4aa;
        }

        .search-box {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 10px 20px;
            width: 300px;
            color: white;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }

        .search-box::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .cards-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.7) 100%);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .card h3 {
            font-size: 18px;
            margin-bottom: 10px;
            color: white;
        }

        .card p {
            font-size: 14px;
            opacity: 0.7;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .card-button {
            background: #00a8ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .card-button:hover {
            background: #0097e6;
            transform: translateY(-2px);
        }

        .card-button.green {
            background: #00d4aa;
        }

        .card-button.green:hover {
            background: #00c7a0;
        }

        /* Weather Card */
        .weather-card {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.7) 100%);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
        }

        .weather-left {
            display: flex;
            flex-direction: column;
        }

        .weather-temp {
            font-size: 72px;
            font-weight: 300;
            color: white;
            line-height: 1;
            margin-bottom: 10px;
        }

        .weather-details {
            font-size: 16px;
            opacity: 0.8;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .weather-chart {
            flex: 1;
            margin: 0 40px;
            height: 60px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .weather-chart-line {
            width: 80%;
            height: 3px;
            background: linear-gradient(90deg, #00d4aa 0%, #00a8ff 50%, #667eea 100%);
            border-radius: 2px;
            position: relative;
        }

        .weather-chart-line::before,
        .weather-chart-line::after {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: #00a8ff;
            border-radius: 50%;
            top: -2.5px;
        }

        .weather-chart-line::before {
            left: 30%;
        }

        .weather-chart-line::after {
            right: 20%;
        }

        .weather-times {
            display: flex;
            flex-direction: column;
            gap: 10px;
            font-size: 14px;
            opacity: 0.7;
            text-align: right;
        }

        /* Right Sidebar */
        .right-sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .music-player {
            background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.7) 100%);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .music-cover {
            width: 100%;
            height: 140px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            margin-bottom: 15px;
            position: relative;
            display: flex;
            align-items: flex-end;
            padding: 15px;
            overflow: hidden;
        }

        .music-cover::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0, 0, 0, 0.3) 0%, transparent 100%);
        }

        .music-title {
            color: white;
            font-size: 14px;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .music-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
        }

        .control-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.1);
        }

        .call-interface {
            background: linear-gradient(135deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.7) 100%);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(15px);
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .call-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #f0f0f0 0%, #d0d0d0 100%);
            margin: 0 auto 20px;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="35" r="15" fill="%23666"/><path d="M25 75 Q25 60 50 60 Q75 60 75 75 Z" fill="%23666"/></svg>');
            background-size: cover;
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .call-name {
            font-size: 18px;
            margin-bottom: 20px;
            color: white;
        }

        .call-status {
            font-size: 14px;
            opacity: 0.7;
            margin-bottom: 20px;
        }

        .call-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .call-btn {
            width: 55px;
            height: 55px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: all 0.3s;
        }

        .call-btn.accept {
            background: #00d4aa;
            color: white;
        }

        .call-btn.accept:hover {
            background: #00c7a0;
            transform: scale(1.1);
        }

        .call-btn.decline {
            background: #ff4757;
            color: white;
        }

        .call-btn.decline:hover {
            background: #ff3742;
            transform: scale(1.1);
        }

        /* Bottom Navigation */
        .bottom-nav {
            grid-column: 1 / -1;
            background: linear-gradient(90deg, rgba(15, 20, 25, 0.9) 0%, rgba(26, 35, 50, 0.7) 100%);
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 0 20px;
        }

        .nav-item {
            text-align: center;
            cursor: pointer;
            padding: 12px 16px;
            border-radius: 12px;
            transition: all 0.3s;
            position: relative;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-item.active {
            background: rgba(0, 212, 170, 0.2);
            color: #00d4aa;
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: #00d4aa;
            border-radius: 2px;
        }

        .nav-item span {
            display: block;
            font-size: 12px;
            margin-top: 6px;
            font-weight: 500;
        }

        .nav-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }

        /* Additional Enhancements */
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 212, 170, 0.05) 0%, rgba(0, 168, 255, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s;
            border-radius: 20px;
        }

        .card:hover::before {
            opacity: 1;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(0, 212, 170, 0.5);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 212, 170, 0.7);
        }

        /* Glow Effects */
        .ai-avatar {
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                box-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
            }
            to {
                box-shadow: 0 0 30px rgba(0, 212, 170, 0.5);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 200px 1fr 280px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>灰犬智慧城市中心驾驶舱</h1>
            <div class="header-nav">
                <span>数据总览</span>
                <span>数据档案</span>
                <span>数据监督</span>
                <span>数据总览</span>
                <span>数据档案</span>
            </div>
            <div class="weather-info">
                <span>☀️ 晴转多云</span>
                <span>18°C/26°C</span>
            </div>
        </div>

        <!-- Left Sidebar - AI Assistant -->
        <div class="left-sidebar">
            <div class="ai-avatar"></div>
            <div class="ai-greeting">
                <strong>Hi! 我是你的AI助手</strong>
            </div>
            <div class="chat-messages">
                <p>are you shining just for me?<br>你是否只为我一个人闪耀</p>
                <p>City of stars,<br>星空之城</p>
                <p>there's so much that i can't see<br>命运太大无世界 目不暇接</p>
                <p>Who knows,<br>谁知道呢</p>
                <p>is this the start of something wonderful and new</p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Main Header -->
            <div class="main-header">
                <h2 class="main-title">某某课件名称</h2>
                <input type="text" class="search-box" placeholder="🔍">
            </div>

            <!-- Function Cards Grid -->
            <div class="cards-grid">
                <div class="card">
                    <div class="card-icon">🚗</div>
                    <h3>道路拥堵</h3>
                    <p>道路拥堵情况监测 13分钟</p>
                    <button class="card-button">切换视角</button>
                </div>

                <div class="card">
                    <div class="card-icon">🚙</div>
                    <h3>小鹏汽车</h3>
                    <p>车辆状态监测 15分钟</p>
                    <button class="card-button green">立即导航</button>
                </div>
            </div>

            <!-- Weather Card -->
            <div class="weather-card">
                <div class="weather-left">
                    <div class="weather-temp">18°</div>
                    <div class="weather-details">
                        <div>↑ 22°C</div>
                        <div>↓ 14°C</div>
                        <div>雾里看花</div>
                    </div>
                </div>
                <div class="weather-chart">
                    <div class="weather-chart-line"></div>
                </div>
                <div class="weather-times">
                    <div>now</div>
                    <div>12:00</div>
                    <div>18:00</div>
                </div>
            </div>

            <!-- Bottom Cards Grid -->
            <div class="cards-grid">
                <div class="card">
                    <div class="card-icon">🚦</div>
                    <h3>路况拥堵</h3>
                    <p>道路拥堵情况监测 13分钟</p>
                    <button class="card-button">切换视角</button>
                </div>

                <div class="card">
                    <div class="card-icon">🚘</div>
                    <h3>小鹏汽车</h3>
                    <p>车辆状态监测 15分钟</p>
                    <button class="card-button green">立即导航</button>
                </div>
            </div>
        </div>

        <!-- Right Sidebar -->
        <div class="right-sidebar">
            <!-- Music Player -->
            <div class="music-player">
                <div class="music-cover">
                    <div class="music-title">Five More Hours</div>
                </div>
                <div class="music-controls">
                    <button class="control-btn">⏸️</button>
                    <button class="control-btn">⏭️</button>
                </div>
            </div>

            <!-- Call Interface -->
            <div class="call-interface">
                <div class="call-avatar"></div>
                <div class="call-name">白教授</div>
                <div class="call-status">来电中...</div>
                <div class="call-actions">
                    <button class="call-btn accept">📞</button>
                    <button class="call-btn decline">📞</button>
                </div>
            </div>

            <!-- Weather Info -->
            <div class="card">
                <div style="text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 15px; color: white;">18°</div>
                    <div style="font-size: 14px; opacity: 0.8; line-height: 1.6;">
                        <div>↑ 22°C</div>
                        <div>↓ 14°C</div>
                        <div>雾里看花</div>
                        <div style="margin-top: 15px; color: #00d4aa; font-size: 16px;">2</div>
                        <div style="color: #00d4aa; font-size: 12px;">微风</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item">
                <div class="nav-icon">📊</div>
                <span>资源推荐</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📋</div>
                <span>资料查询</span>
            </div>
            <div class="nav-item active">
                <div class="nav-icon">🚗</div>
                <span>随堂练习</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📝</div>
                <span>课后小结</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">❓</div>
                <span>点名提问</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">🎯</div>
                <span>课堂互动</span>
            </div>
            <div class="nav-item">
                <div class="nav-icon">⚖️</div>
                <span>备课平台</span>
            </div>
        </div>
    </div>
</body>
</html>